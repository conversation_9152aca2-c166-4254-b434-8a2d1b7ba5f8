import 'package:flutter/material.dart';
import 'RenewRequest.dart';

/// Test screen for RenewRequest functionality
/// This allows testing different scenarios without needing real permits
class RenewRequestTestScreen extends StatelessWidget {
  const RenewRequestTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Renewal Request'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Different Scenarios',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // Test with validation (normal flow)
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RenewRequestScreen(
                      permitId: 1,
                      skipValidation: false,
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text('Test with Validation (Permit ID: 1)'),
            ),
            
            const SizedBox(height: 12),
            
            // Test without validation (skip validation)
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RenewRequestScreen(
                      permitId: 999,
                      skipValidation: true,
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text('Test without Validation (Permit ID: 999)'),
            ),
            
            const SizedBox(height: 12),
            
            // Test with different permit ID
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RenewRequestScreen(
                      permitId: 2,
                      skipValidation: false,
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text('Test with Different ID (Permit ID: 2)'),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Blue button: Tests normal flow with permit validation\n'
              '• Green button: Skips validation for testing purposes\n'
              '• Purple button: Tests with a different permit ID\n\n'
              'Use the green button if you encounter permit validation errors.',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
